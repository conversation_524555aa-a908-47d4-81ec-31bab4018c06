// Test script to verify that tools are being listed correctly
const { spawn } = require('child_process');

console.log('🧪 Testing MCP Tools Listing...\n');

// Start the game state server
const serverProcess = spawn('node', ['dist/index.js'], {
  cwd: './game-state-server',
  stdio: ['pipe', 'pipe', 'pipe']
});

let serverOutput = '';
let serverError = '';

serverProcess.stdout.on('data', (data) => {
  serverOutput += data.toString();
});

serverProcess.stderr.on('data', (data) => {
  serverError += data.toString();
});

// Send a list tools request
setTimeout(() => {
  console.log('📤 Sending list tools request...');
  
  const listToolsRequest = {
    jsonrpc: "2.0",
    id: 1,
    method: "tools/list",
    params: {}
  };
  
  serverProcess.stdin.write(JSON.stringify(listToolsRequest) + '\n');
  
  // Wait for response
  setTimeout(() => {
    console.log('📥 Server output:');
    console.log(serverOutput);
    
    if (serverError) {
      console.log('❌ Server errors:');
      console.log(serverError);
    }
    
    // Check if tools are listed
    if (serverOutput.includes('create_character') || serverOutput.includes('tools')) {
      console.log('✅ Tools are being listed correctly!');
    } else {
      console.log('❌ Tools are not being listed');
    }
    
    serverProcess.kill();
  }, 2000);
}, 1000);

import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import { CallToolRequestSchema, ListToolsRequestSchema } from '@modelcontextprotocol/sdk/types.js';

console.log("Initializing minimal server...");
const server = new Server({ name: 'rpg-game-state-server', version: '2.1.0' }, { capabilities: { tools: {} } });

const toolDefinitions = [
  {
    name: 'test_tool',
    description: 'A test tool to verify the server is working.',
    inputSchema: {
      type: 'object',
      properties: {
        message: { type: 'string', description: 'Test message' }
      },
      required: ['message']
    }
  }
];

console.log("Registering ListToolsRequestSchema handler...");
server.setRequestHandler(ListToolsRequestSchema, async () => {
  console.log("ListToolsRequestSchema handler called!");
  return { tools: toolDefinitions };
});

console.log("Registering CallToolRequestSchema handler...");
server.setRequestHandler(CallToolRequestSchema, async (request: any) => {
  const { name, arguments: args } = request.params;
  console.log(`Handling tool request: ${name}`);
  
  if (name === 'test_tool') {
    return {
      content: [{ type: 'text', text: `Test tool called with message: ${args.message}` }]
    };
  }
  
  return { content: [{ type: 'text', text: `Unknown tool: ${name}` }], isError: true };
});

console.log("Initializing transport...");
const stdioTransport = new StdioServerTransport();
console.log("Transport initialized.");

server.connect(stdioTransport);
console.error('Minimal RPG Game State MCP Server running on stdio');

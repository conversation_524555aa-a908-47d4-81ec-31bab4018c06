// File: game-state-server/src/db.ts

import Database from 'better-sqlite3';
import { existsSync, mkdirSync } from 'fs';
import { join } from 'path';
import { ANTAGONIST_TEMPLATES } from './antagonists.js';
import { HealthTracker, DamageObject } from './health-tracker.js';

// --- Interface Definitions ---
interface CharacterRow {
  [key: string]: any; // Allow dynamic access for trait improvements, etc.
  id: number;
  name: string;
  concept?: string | null;
  game_line: string;
  strength: number; dexterity: number; stamina: number;
  charisma: number; manipulation: number; appearance: number;
  perception: number; intelligence: number; wits: number;
  willpower_current: number; willpower_permanent: number;
  health_levels: string; // JSON
  blood_pool_current?: number;
  // ... and all other game-line specific fields
}

export interface AntagonistRow {
  id: number;
  name: string;
  template: string;
  concept: string;
  game_line: string;
  strength: number;
  dexterity: number;
  stamina: number;
  charisma: number;
  manipulation: number;
  appearance: number;
  perception: number;
  intelligence: number;
  wits: number;
  willpower_current: number;
  willpower_permanent: number;
  health_levels: string;
  blood_pool_current: number;
  notes: string;
}

export interface NpcRow {
  id: number;
  name: string;
  template: string;
  concept: string;
  game_line: string;
  strength: number;
  dexterity: number;
  stamina: number;
  charisma: number;
  manipulation: number;
  appearance: number;
  perception: number;
  intelligence: number;
  wits: number;
  willpower_current: number;
  willpower_permanent: number;
  health_levels: string;
  blood_pool_current: number;
  notes: string;
}

// Create data directory in workspace
const DATA_DIR = join(process.cwd(), 'data');
if (!existsSync(DATA_DIR)) {
  mkdirSync(DATA_DIR, { recursive: true });
}
const DB_PATH = join(DATA_DIR, 'game-state.db');

export class GameDatabase {
  private db: Database.Database;
  private resourceLocks: Map<string, { timestamp: number; operation: string }> = new Map();
  private characterLocks: Map<number, { timestamp: number; operation: string }> = new Map();
  private readonly LOCK_TIMEOUT_MS = 5000; // 5 second timeout for resource locks
  private readonly CHARACTER_LOCK_TIMEOUT_MS = 3000; // 3 second timeout for character operations

  constructor() {
    this.db = new Database(DB_PATH);
    this.db.pragma('journal_mode = WAL');
    this.initializeSchema();
  }

  private initializeSchema() {
    // --- oWoD-centric Core Character Table ---
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS characters (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL UNIQUE,
        concept TEXT,
        game_line TEXT NOT NULL,
        -- Core Attributes
        strength INTEGER DEFAULT 1, dexterity INTEGER DEFAULT 1, stamina INTEGER DEFAULT 1,
        charisma INTEGER DEFAULT 1, manipulation INTEGER DEFAULT 1, appearance INTEGER DEFAULT 1,
        perception INTEGER DEFAULT 1, intelligence INTEGER DEFAULT 1, wits INTEGER DEFAULT 1,
        -- Core Traits
        willpower_current INTEGER DEFAULT 1,
        willpower_permanent INTEGER DEFAULT 1,
        health_levels TEXT NOT NULL, -- JSON
        experience INTEGER DEFAULT 0
      );
    `);

    // --- Database Migrations ---
    this.runMigrations();

    // --- Relational Tables ---
    this.db.exec(`CREATE TABLE IF NOT EXISTS character_abilities (character_id INTEGER, ability_name TEXT, ability_type TEXT, rating INTEGER, specialty TEXT, PRIMARY KEY(character_id, ability_name), FOREIGN KEY (character_id) REFERENCES characters(id) ON DELETE CASCADE);`);
    this.db.exec(`CREATE TABLE IF NOT EXISTS character_disciplines (character_id INTEGER, discipline_name TEXT, rating INTEGER, specialty TEXT, PRIMARY KEY(character_id, discipline_name), FOREIGN KEY (character_id) REFERENCES characters(id) ON DELETE CASCADE);`);
    this.db.exec(`CREATE TABLE IF NOT EXISTS character_arts (character_id INTEGER, art_name TEXT, rating INTEGER, specialty TEXT, PRIMARY KEY(character_id, art_name), FOREIGN KEY (character_id) REFERENCES characters(id) ON DELETE CASCADE);`);
    this.db.exec(`CREATE TABLE IF NOT EXISTS character_realms (character_id INTEGER, realm_name TEXT, rating INTEGER, specialty TEXT, PRIMARY KEY(character_id, realm_name), FOREIGN KEY (character_id) REFERENCES characters(id) ON DELETE CASCADE);`);
    this.db.exec(`CREATE TABLE IF NOT EXISTS character_gifts (character_id INTEGER, gift_name TEXT, rank INTEGER, PRIMARY KEY(character_id, gift_name), FOREIGN KEY (character_id) REFERENCES characters(id) ON DELETE CASCADE);`);
    this.db.exec(`CREATE TABLE IF NOT EXISTS character_spheres (character_id INTEGER, sphere_name TEXT, rating INTEGER, specialty TEXT, PRIMARY KEY(character_id, sphere_name), FOREIGN KEY (character_id) REFERENCES characters(id) ON DELETE CASCADE);`);
    this.db.exec(`CREATE TABLE IF NOT EXISTS character_derangements (id INTEGER PRIMARY KEY, character_id INTEGER, derangement TEXT, description TEXT, UNIQUE(character_id, derangement), FOREIGN KEY (character_id) REFERENCES characters(id) ON DELETE CASCADE);`);

    // --- Inventory Table ---
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS inventory (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        character_id INTEGER NOT NULL,
        item_name TEXT NOT NULL,
        item_type TEXT, -- e.g., 'Weapon', 'Trinket', 'Consumable'
        quantity INTEGER DEFAULT 1,
        description TEXT,
        properties TEXT, -- JSON for stats like weapon damage, etc.
        FOREIGN KEY (character_id) REFERENCES characters(id) ON DELETE CASCADE
      );
    `);

    // --- World & Story Persistence Tables ---
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS world_state (
        id INTEGER PRIMARY KEY, -- Use a single row for the whole campaign for simplicity
        location TEXT,
        notes TEXT,
        data TEXT, -- Flexible JSON blob for NPCs, events, etc.
        last_updated DATETIME DEFAULT CURRENT_TIMESTAMP
      );
    `);

    this.db.exec(`
      CREATE TABLE IF NOT EXISTS story_progress (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        chapter INTEGER,
        scene TEXT,
        summary TEXT,
        timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
      );
    `);

    // --- Game-line Specific Trait Tables (modular) ---
    // Vampire
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS character_vampire_traits (
        character_id INTEGER PRIMARY KEY,
        clan TEXT,
        generation INTEGER,
        blood_pool_current INTEGER,
        blood_pool_max INTEGER,
        humanity INTEGER,
        FOREIGN KEY (character_id) REFERENCES characters(id) ON DELETE CASCADE
      );`);
    // Werewolf
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS character_werewolf_traits (
        character_id INTEGER PRIMARY KEY,
        breed TEXT,
        auspice TEXT,
        tribe TEXT,
        gnosis_current INTEGER,
        gnosis_permanent INTEGER,
        rage_current INTEGER,
        rage_permanent INTEGER,
        renown_glory INTEGER,
        renown_honor INTEGER,
        renown_wisdom INTEGER,
        FOREIGN KEY (character_id) REFERENCES characters(id) ON DELETE CASCADE
      );`);
    // Mage
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS character_mage_traits (
        character_id INTEGER PRIMARY KEY,
        tradition_convention TEXT,
        arete INTEGER,
        quintessence INTEGER,
        paradox INTEGER,
        FOREIGN KEY (character_id) REFERENCES characters(id) ON DELETE CASCADE
      );`);
    // Changeling
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS character_changeling_traits (
        character_id INTEGER PRIMARY KEY,
        kith TEXT,
        seeming TEXT,
        glamour_current INTEGER,
        glamour_permanent INTEGER,
        banality_permanent INTEGER,
        FOREIGN KEY (character_id) REFERENCES characters(id) ON DELETE CASCADE
      );`);

    // ADDITION: Experience Ledger table for character XP transactions
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS experience_ledger (
        id INTEGER PRIMARY KEY,
        character_id INTEGER NOT NULL,
        amount INTEGER NOT NULL,
        reason TEXT,
        timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (character_id) REFERENCES characters(id) ON DELETE CASCADE
      );
    `);

    // --- Refactored Modular Antagonists/NPCs Table ---
    this.db.exec(`DROP TABLE IF EXISTS npcs;`); // Backup data first!
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS npcs (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        template TEXT,
        concept TEXT,
        game_line TEXT NOT NULL,
        strength INTEGER DEFAULT 1, dexterity INTEGER DEFAULT 1, stamina INTEGER DEFAULT 1,
        charisma INTEGER DEFAULT 1, manipulation INTEGER DEFAULT 1, appearance INTEGER DEFAULT 1,
        perception INTEGER DEFAULT 1, intelligence INTEGER DEFAULT 1, wits INTEGER DEFAULT 1,
        willpower_current INTEGER DEFAULT 1,
        willpower_permanent INTEGER DEFAULT 1,
        health_levels TEXT NOT NULL, -- JSON
        notes TEXT
      );
    `);
    // Modular splat trait tables for NPCs -- structure mirrors player traits
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS npc_vampire_traits (
        npc_id INTEGER PRIMARY KEY,
        clan TEXT,
        generation INTEGER,
        blood_pool_current INTEGER,
        blood_pool_max INTEGER,
        humanity INTEGER,
        FOREIGN KEY (npc_id) REFERENCES npcs(id) ON DELETE CASCADE
      );`);
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS npc_werewolf_traits (
        npc_id INTEGER PRIMARY KEY,
        breed TEXT,
        auspice TEXT,
        tribe TEXT,
        gnosis_current INTEGER,
        gnosis_permanent INTEGER,
        rage_current INTEGER,
        rage_permanent INTEGER,
        renown_glory INTEGER,
        renown_honor INTEGER,
        renown_wisdom INTEGER,
        FOREIGN KEY (npc_id) REFERENCES npcs(id) ON DELETE CASCADE
      );`);
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS npc_mage_traits (
        npc_id INTEGER PRIMARY KEY,
        tradition_convention TEXT,
        arete INTEGER,
        quintessence INTEGER,
        paradox INTEGER,
        FOREIGN KEY (npc_id) REFERENCES npcs(id) ON DELETE CASCADE
      );`);
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS npc_changeling_traits (
        npc_id INTEGER PRIMARY KEY,
        kith TEXT,
        seeming TEXT,
        glamour_current INTEGER,
        glamour_permanent INTEGER,
        banality_permanent INTEGER,
        FOREIGN KEY (npc_id) REFERENCES npcs(id) ON DELETE CASCADE
      );`);

    // --- Initiative Tracking Table ---
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS initiative_order (
        scene_id TEXT NOT NULL,
        character_id INTEGER,
        npc_id INTEGER,
        actor_name TEXT NOT NULL,
        initiative_score INTEGER NOT NULL,
        turn_order INTEGER NOT NULL,
        PRIMARY KEY(scene_id, turn_order),
        FOREIGN KEY(character_id) REFERENCES characters(id) ON DELETE SET NULL,
        FOREIGN KEY(npc_id) REFERENCES npcs(id) ON DELETE SET NULL
      );
    `);
      // --- Turn Management Table for Combat Scenes ---
      this.db.exec(`
        CREATE TABLE IF NOT EXISTS scenes (
          scene_id TEXT PRIMARY KEY,
          current_round INTEGER NOT NULL DEFAULT 1,
          current_turn_order INTEGER NOT NULL DEFAULT 0
        );
      `);
    // --- Generic Status Effects Table ---
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS status_effects (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        character_id INTEGER,
        npc_id INTEGER,
        effect_name TEXT NOT NULL,
        description TEXT,
        mechanical_effect TEXT,
        duration_type TEXT DEFAULT 'indefinite',
        duration_value INTEGER,
        FOREIGN KEY (character_id) REFERENCES characters(id) ON DELETE CASCADE,
        FOREIGN KEY (npc_id) REFERENCES npcs(id) ON DELETE CASCADE
      );
    `);
  }

  createCharacter(data: any) {
    if (!['vampire', 'werewolf', 'mage', 'changeling'].includes(data.game_line)) {
      throw new Error(`Invalid game_line: ${data.game_line}. Must be one of: vampire, werewolf, mage, changeling`);
    }

    const health_levels = data.health_levels || { bruised: 0, hurt: 0, injured: 0, wounded: 0, mauled: 0, crippled: 0, incapacitated: 0 };
    let charId: number | undefined = undefined;

    // Transactional logic: all sub-table inserts are done atomically
    charId = this.db.transaction(() => {
      let localCharId: number;
      // Insert core character data
      const stmt = this.db.prepare(`
        INSERT INTO characters (
          name, concept, game_line,
          strength, dexterity, stamina, charisma, manipulation, appearance,
          perception, intelligence, wits,
          willpower_current, willpower_permanent, health_levels, experience
        ) VALUES (
          @name, @concept, @game_line,
          @strength, @dexterity, @stamina, @charisma, @manipulation, @appearance,
          @perception, @intelligence, @wits,
          @willpower_current, @willpower_permanent, @health_levels, @experience
        )
      `);

      const result = stmt.run({
        name: data.name,
        concept: data.concept || null,
        game_line: data.game_line,
        strength: data.strength || 1,
        dexterity: data.dexterity || 1,
        stamina: data.stamina || 1,
        charisma: data.charisma || 1,
        manipulation: data.manipulation || 1,
        appearance: data.appearance || 1,
        perception: data.perception || 1,
        intelligence: data.intelligence || 1,
        wits: data.wits || 1,
        willpower_current: data.willpower_current || 1,
        willpower_permanent: data.willpower_permanent || 1,
        health_levels: JSON.stringify(health_levels),
        experience: data.experience || 0
      });
      localCharId = result.lastInsertRowid as number;

      // --- Insert into game-line-specific tables ---

      switch (data.game_line) {
        case 'vampire':
          this.db.prepare(`
            INSERT INTO character_vampire_traits
            (character_id, clan, generation, blood_pool_current, blood_pool_max, humanity)
            VALUES (?, ?, ?, ?, ?, ?)
          `).run(
            localCharId,
            data.clan ?? null,
            data.generation ?? 13,  // Default generation for new vampires
            data.blood_pool_current ?? 10,  // Default current blood pool
            data.blood_pool_max ?? 10,      // Default max blood pool
            data.humanity ?? 7              // Default humanity
          );
          break;
        case 'werewolf':
          this.db.prepare(`
            INSERT INTO character_werewolf_traits
            (character_id, breed, auspice, tribe, gnosis_current, gnosis_permanent, rage_current, rage_permanent, renown_glory, renown_honor, renown_wisdom)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
          `).run(
            localCharId,
            data.breed ?? null, data.auspice ?? null, data.tribe ?? null,
            data.gnosis_current ?? 3, data.gnosis_permanent ?? 3,  // Default gnosis
            data.rage_current ?? 1, data.rage_permanent ?? 1,      // Default rage
            data.renown_glory ?? 0, data.renown_honor ?? 0, data.renown_wisdom ?? 0  // Default renown
          );
          break;
        case 'mage':
          this.db.prepare(`
            INSERT INTO character_mage_traits
            (character_id, tradition_convention, arete, quintessence, paradox)
            VALUES (?, ?, ?, ?, ?)
          `).run(
            localCharId,
            data.tradition_convention ?? null,
            data.arete ?? 1,         // Default arete
            data.quintessence ?? 0,  // Default quintessence
            data.paradox ?? 0        // Default paradox
          );
          break;
        case 'changeling':
          this.db.prepare(`
            INSERT INTO character_changeling_traits
            (character_id, kith, seeming, glamour_current, glamour_permanent, banality_permanent)
            VALUES (?, ?, ?, ?, ?, ?)
          `).run(
            localCharId,
            data.kith ?? null, data.seeming ?? null,
            data.glamour_current ?? 4, data.glamour_permanent ?? 4,  // Default glamour
            data.banality_permanent ?? 3  // Default banality
          );
          break;
        // Additional splats can be added here in similar fashion
      }

      // Changeling-specific: arts/reals
      if (data.game_line === "changeling") {
        if (data.arts && Array.isArray(data.arts)) {
          const artStmt = this.db.prepare(
            `INSERT INTO character_arts (character_id, art_name, rating) VALUES (?, ?, ?)`
          );
          for (const a of data.arts) {
            artStmt.run(localCharId, a.art_name ?? a.name ?? a.label ?? '', Number(a.rating) || 0);
          }
        }
        if (data.realms && Array.isArray(data.realms)) {
          const realmStmt = this.db.prepare(
            `INSERT INTO character_realms (character_id, realm_name, rating) VALUES (?, ?, ?)`
          );
          for (const r of data.realms) {
            realmStmt.run(localCharId, r.realm_name ?? r.name ?? r.label ?? '', Number(r.rating) || 0);
          }
        }
      }

      // Example sub-table transactional inserts; expand for all relations as needed
      if (data.abilities && Array.isArray(data.abilities)) {
        const abilityStmt = this.db.prepare(
          `INSERT INTO character_abilities (character_id, ability_name, ability_type, rating, specialty)
           VALUES (?, ?, ?, ?, ?)`
        );
        for (const ability of data.abilities) {
          abilityStmt.run(localCharId, ability.name, ability.type, ability.rating, ability.specialty ?? null);
        }
      }
      if (data.disciplines && Array.isArray(data.disciplines)) {
        const discStmt = this.db.prepare(
          `INSERT INTO character_disciplines (character_id, discipline_name, rating)
           VALUES (?, ?, ?)`
        );
        for (const d of data.disciplines) {
          discStmt.run(localCharId, d.name, d.rating);
        }
      }
      // ... perform additional transactional inserts for arts, realms, gifts, etc., as needed
      return localCharId;
    })();

    return this.getCharacterById(charId!);
  }
    
  createAntagonist(template_name: string, custom_name?: string) {
    const template = (ANTAGONIST_TEMPLATES as any)[template_name];
    if (!template) return null;
    // Fill missing health_levels from default if template omits it
    const defaultHealthLevels = { bruised: 0, hurt: 0, injured: 0, wounded: 0, mauled: 0, crippled: 0, incapacitated: 0 };
    const data = {
      ...template,
      name: custom_name || template.name || template_name,
      template: template_name,
      health_levels: template.health_levels ?? defaultHealthLevels
    };
    let npcId: number | undefined = undefined;

    // Validate required fields after filling health_levels
    if (!data.name || !data.game_line || !data.health_levels) {
      console.error("Missing required fields in antagonist template:", template_name, data);
      return null;
    }

    
    // Transaction to insert core NPC and relational data
    this.db.transaction(() => {
      // 1. Insert into new lean core npcs table (no game-line-specific splat traits here)
      const stmt = this.db.prepare(`
        INSERT INTO npcs (
          name, template, concept, game_line,
          strength, dexterity, stamina, charisma, manipulation, appearance,
          perception, intelligence, wits,
          willpower_current, willpower_permanent, health_levels, notes
        ) VALUES (
          @name, @template, @concept, @game_line,
          @strength, @dexterity, @stamina, @charisma, @manipulation, @appearance,
          @perception, @intelligence, @wits,
          @willpower_current, @willpower_permanent, @health_levels, @notes
        )
      `);
      const result = stmt.run({
        name: data.name,
        template: data.template,
        concept: data.concept || null,
        game_line: data.game_line,
        strength: data.strength || 1,
        dexterity: data.dexterity || 1,
        stamina: data.stamina || 1,
        charisma: data.charisma || 1,
        manipulation: data.manipulation || 1,
        appearance: data.appearance || 1,
        perception: data.perception || 1,
        intelligence: data.intelligence || 1,
        wits: data.wits || 1,
        willpower_current: data.willpower_current || 1,
        willpower_permanent: data.willpower_permanent || 1,
        health_levels: JSON.stringify(data.health_levels ?? {}),
        notes: data.notes || null
      });
      npcId = result.lastInsertRowid as number;
      // 2. Modular splat trait tables
      switch (template.game_line) {
        case 'vampire':
          this.db.prepare(`
            INSERT INTO npc_vampire_traits
            (npc_id, clan, generation, blood_pool_current, blood_pool_max, humanity)
            VALUES (?, ?, ?, ?, ?, ?)
          `).run(
            npcId,
            template.clan ?? null,
            template.generation ?? null,
            template.blood_pool_current ?? null,
            template.blood_pool_max ?? null,
            template.humanity ?? null
          );
          break;
        case 'werewolf':
          this.db.prepare(`
            INSERT INTO npc_werewolf_traits
            (npc_id, breed, auspice, tribe, gnosis_current, gnosis_permanent, rage_current, rage_permanent, renown_glory, renown_honor, renown_wisdom)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
          `).run(
            npcId,
            template.breed ?? null,
            template.auspice ?? null,
            template.tribe ?? null,
            template.gnosis_current ?? null,
            template.gnosis_permanent ?? null,
            template.rage_current ?? null,
            template.rage_permanent ?? null,
            template.renown_glory ?? null,
            template.renown_honor ?? null,
            template.renown_wisdom ?? null
          );
          break;
        case 'mage':
          this.db.prepare(`
            INSERT INTO npc_mage_traits
            (npc_id, tradition_convention, arete, quintessence, paradox)
            VALUES (?, ?, ?, ?, ?)
          `).run(
            npcId,
            template.tradition_convention ?? null,
            template.arete ?? null,
            template.quintessence ?? null,
            template.paradox ?? null
          );
          break;
        case 'changeling':
          this.db.prepare(`
            INSERT INTO npc_changeling_traits
            (npc_id, kith, seeming, glamour_current, glamour_permanent, banality_permanent)
            VALUES (?, ?, ?, ?, ?, ?)
          `).run(
            npcId,
            template.kith ?? null,
            template.seeming ?? null,
            template.glamour_current ?? null,
            template.glamour_permanent ?? null,
            template.banality_permanent ?? null
          );
          break;
        // Expand for other splats as needed
      }

      // 3. Relational data (abilities, disciplines, gifts, spheres, arts, realms)
      if (template.abilities) {
        const abilities = template.abilities;
        const abilityStmt = this.db.prepare(`INSERT INTO character_abilities (character_id, ability_name, ability_type, rating, specialty) VALUES (?, ?, ?, ?, NULL)`);
        if (abilities.talents) {
          for (const [name, rating] of Object.entries(abilities.talents)) {
            abilityStmt.run(npcId, name, 'Talent', rating);
          }
        }
        if (abilities.skills) {
          for (const [name, rating] of Object.entries(abilities.skills)) {
            abilityStmt.run(npcId, name, 'Skill', rating);
          }
        }
        if (abilities.knowledges) {
          for (const [name, rating] of Object.entries(abilities.knowledges)) {
            abilityStmt.run(npcId, name, 'Knowledge', rating);
          }
        }
      }

      // 4. Supernatural powers (disciplines, gifts, spheres, arts, realms)
      if (template.supernatural?.disciplines) {
        const discStmt = this.db.prepare(`INSERT INTO character_disciplines (character_id, discipline_name, rating) VALUES (?, ?, ?)`);
        for (const [name, rating] of Object.entries(template.supernatural.disciplines)) {
          discStmt.run(npcId, name, rating);
        }
      }
      if (template.supernatural?.gifts) {
        const giftStmt = this.db.prepare(`INSERT INTO character_gifts (character_id, gift_name, rank) VALUES (?, ?, ?)`);
        for (const [name, rank] of Object.entries(template.supernatural.gifts)) {
          giftStmt.run(npcId, name, rank);
        }
      }
      if (template.supernatural?.spheres) {
        const sphStmt = this.db.prepare(`INSERT INTO character_spheres (character_id, sphere_name, rating) VALUES (?, ?, ?)`);
        for (const [name, rating] of Object.entries(template.supernatural.spheres)) {
          sphStmt.run(npcId, name, rating);
        }
      }
      if (template.supernatural?.arts) {
        const artStmt = this.db.prepare(`INSERT INTO character_arts (character_id, art_name, rating) VALUES (?, ?, ?)`);
        for (const [name, rating] of Object.entries(template.supernatural.arts)) {
          artStmt.run(npcId, name, rating);
        }
      }
      if (template.supernatural?.realms) {
        const realmStmt = this.db.prepare(`INSERT INTO character_realms (character_id, realm_name, rating) VALUES (?, ?, ?)`);
        for (const [name, rating] of Object.entries(template.supernatural.realms)) {
          realmStmt.run(npcId, name, rating);
        }
      }
    })();

    return this.getAntagonistById(npcId!);
  }

  removeStatusEffect(effect_id: number): boolean {
    const stmt = this.db.prepare(`DELETE FROM status_effects WHERE id = ?`);
    const res = stmt.run(effect_id);
    return res.changes > 0;
  }

  listStatusEffects(target_type: string, target_id: number): any[] {
    if (!target_type || !target_id) return [];
    const col = target_type === "character"
      ? "character_id"
      : target_type === "npc"
      ? "npc_id"
      : null;
    if (!col) return [];
    return this.db.prepare(
      `SELECT * FROM status_effects WHERE ${col} = ?`
    ).all(target_id).map((e: any) => ({
      ...e,
      mechanical_effect: e.mechanical_effect ? JSON.parse(e.mechanical_effect) : {}
    }));
  }

  addStatusEffect(opts: {
    target_type: 'character' | 'npc',
    target_id: number,
    effect_name: string,
    description?: string,
    mechanical_effect?: any,
    duration_type?: string,
    duration_value?: number | null
  }): number {
    const {
      target_type, target_id, effect_name,
      description = '', mechanical_effect = {},
      duration_type = 'indefinite', duration_value = null
    } = opts;
    const targetKey = target_type === 'character' ? 'character_id' : 'npc_id';
    const dbres = this.db.prepare(
      `INSERT INTO status_effects (${targetKey}, effect_name, description, mechanical_effect, duration_type, duration_value)
       VALUES (?, ?, ?, ?, ?, ?)`
    ).run(
      target_id,
      effect_name,
      description,
      JSON.stringify(mechanical_effect ?? {}),
      duration_type,
      duration_value
    );
    return dbres.lastInsertRowid as number;
  }

  getCharacterByName(name: string): CharacterRow | null {
    const row = this.db.prepare('SELECT * FROM characters WHERE name = ?').get(name);
    return row ? (row as CharacterRow) : null;
  }

  getAntagonistByName(name: string): AntagonistRow | null {
    const row = this.db.prepare('SELECT * FROM npcs WHERE name = ?').get(name);
    return row ? (row as AntagonistRow) : null;
  }

  getAntagonistById(id: number): AntagonistRow | null {
    const row = this.db.prepare('SELECT * FROM npcs WHERE id = ?').get(id);
    return row ? (row as AntagonistRow) : null;
  }

  getCharacterById(id: number): CharacterRow | null {
    const row = this.db.prepare('SELECT * FROM characters WHERE id = ?').get(id);
    return row ? (row as CharacterRow) : null;
  }

  private runMigrations() {
    // Check if experience column exists, if not add it
    try {
      this.db.prepare('SELECT experience FROM characters LIMIT 1').get();
      console.log('Experience column already exists');
    } catch (error) {
      // Column doesn't exist, add it
      console.log('Adding experience column to characters table...');
      try {
        this.db.exec('ALTER TABLE characters ADD COLUMN experience INTEGER DEFAULT 0');
        console.log('✅ Experience column added successfully');
      } catch (alterError: any) {
        console.log('⚠️ Could not add experience column:', alterError.message);
      }
    }

    // Check if power_stat_rating column exists, if not add it
    try {
      this.db.prepare('SELECT power_stat_rating FROM characters LIMIT 1').get();
      console.log('Power stat rating column already exists');
    } catch (error) {
      // Column doesn't exist, add it
      console.log('Adding power_stat_rating column to characters table...');
      try {
        this.db.exec('ALTER TABLE characters ADD COLUMN power_stat_rating INTEGER DEFAULT 0');
        console.log('✅ Power stat rating column added successfully');
      } catch (alterError: any) {
        console.log('⚠️ Could not add power_stat_rating column:', alterError.message);
      }
    }

    // Check if power_stat_name column exists, if not add it
    try {
      this.db.prepare('SELECT power_stat_name FROM characters LIMIT 1').get();
      console.log('Power stat name column already exists');
    } catch (error) {
      // Column doesn't exist, add it
      console.log('Adding power_stat_name column to characters table...');
      try {
        this.db.exec('ALTER TABLE characters ADD COLUMN power_stat_name TEXT');
        console.log('✅ Power stat name column added successfully');
      } catch (alterError: any) {
        console.log('⚠️ Could not add power_stat_name column:', alterError.message);
      }
    }
  }

  // Full character data assembly with all splat-specific traits
  getCharacter(id: number): (CharacterRow & {
    abilities: any[];
    disciplines: any[];
    arts?: any[];
    realms?: any[];
    spheres?: any[];
    gifts?: any[];
    [key: string]: any;
  }) | null {
    const char = this.db.prepare('SELECT * FROM characters WHERE id = ?').get(id) as CharacterRow | undefined;
    if (!char) return null;

    let extraData = {};
    switch (char.game_line) {
      case 'vampire':
        extraData = this.db.prepare('SELECT * FROM character_vampire_traits WHERE character_id = ?').get(id) || {};
        break;
      case 'werewolf':
        extraData = this.db.prepare('SELECT * FROM character_werewolf_traits WHERE character_id = ?').get(id) || {};
        break;
      case 'mage':
        extraData = this.db.prepare('SELECT * FROM character_mage_traits WHERE character_id = ?').get(id) || {};
        break;
      case 'changeling':
        extraData = this.db.prepare('SELECT * FROM character_changeling_traits WHERE character_id = ?').get(id) || {};
        break;
      // Add more splats as needed here
    }

    // Fetch all related data
    const abilities = this.db.prepare('SELECT * FROM character_abilities WHERE character_id = ?').all(id);
    const disciplines = this.db.prepare('SELECT * FROM character_disciplines WHERE character_id = ?').all(id);
    const arts = this.db.prepare('SELECT * FROM character_arts WHERE character_id = ?').all(id);
    const realms = this.db.prepare('SELECT * FROM character_realms WHERE character_id = ?').all(id);
    const spheres = this.db.prepare('SELECT * FROM character_spheres WHERE character_id = ?').all(id);
    const gifts = this.db.prepare('SELECT * FROM character_gifts WHERE character_id = ?').all(id);

    return {
      ...char,
      ...extraData,
      health_levels: JSON.parse(char.health_levels),
      abilities,
      disciplines,
      arts,
      realms,
      spheres,
      gifts
    };
  }

  // Character locking for serialized updates
  private acquireCharacterLock(character_id: number, operation: string): boolean {
    const now = Date.now();

    // Clean up expired locks
    this.cleanupExpiredCharacterLocks();

    // Check if character is already locked
    const existingLock = this.characterLocks.get(character_id);
    if (existingLock) {
      // Check if lock has expired
      if (now - existingLock.timestamp > this.CHARACTER_LOCK_TIMEOUT_MS) {
        this.characterLocks.delete(character_id);
      } else {
        return false; // Character is locked
      }
    }

    // Acquire the lock
    this.characterLocks.set(character_id, { timestamp: now, operation });
    return true;
  }

  private releaseCharacterLock(character_id: number): void {
    this.characterLocks.delete(character_id);
  }

  private cleanupExpiredCharacterLocks(): void {
    const now = Date.now();
    for (const [id, lock] of this.characterLocks.entries()) {
      if (now - lock.timestamp > this.CHARACTER_LOCK_TIMEOUT_MS) {
        this.characterLocks.delete(id);
      }
    }
  }

  updateCharacter(character_id: number, updates: any): any {
    // Acquire character lock to prevent concurrent updates
    if (!this.acquireCharacterLock(character_id, 'update')) {
      throw new Error(`Character ${character_id} is currently being updated by another operation. Please try again.`);
    }

    try {
      // Define which fields belong to the main characters table vs splat-specific tables
      const coreFields = [
        'name', 'concept', 'game_line', 'strength', 'dexterity', 'stamina',
        'charisma', 'manipulation', 'appearance', 'perception', 'intelligence', 'wits',
        'willpower_current', 'willpower_permanent', 'health_levels', 'experience'
      ];

      const vampireFields = ['clan', 'generation', 'blood_pool_current', 'blood_pool_max', 'humanity'];
      const werewolfFields = ['breed', 'auspice', 'tribe', 'rage_current', 'rage_permanent', 'gnosis_current', 'gnosis_permanent', 'renown_glory', 'renown_honor', 'renown_wisdom'];
      const mageFields = ['tradition_convention', 'arete', 'quintessence', 'paradox'];
      const changelingFields = ['kith', 'seeming', 'glamour_current', 'glamour_permanent', 'banality_permanent'];

      const allValidFields = [...coreFields, ...vampireFields, ...werewolfFields, ...mageFields, ...changelingFields];

      // Filter to only valid fields
      const validUpdates = Object.keys(updates).filter(k => allValidFields.includes(k));
      if (validUpdates.length === 0) return null;

      // Separate core fields from splat-specific fields
      const coreUpdates = Object.keys(updates).filter(k => coreFields.includes(k));

      const result = this.db.transaction(() => {
        // Update main character table with core fields only
        if (coreUpdates.length > 0) {
          const values = coreUpdates.map(f =>
            (f === 'health_levels' && typeof updates[f] === 'object')
              ? JSON.stringify(updates[f])
              : updates[f]
          );
          const setClause = coreUpdates.map(f => `\`${f}\` = ?`).join(', ');
          this.db.prepare(`UPDATE characters SET ${setClause} WHERE id = ?`).run(...values, character_id);
        }

        // Update splat-specific tables if needed
        const char = this.getCharacterById(character_id);
        if (char) {
          switch (char.game_line) {
            case 'vampire':
              const vampireUpdates = Object.keys(updates).filter(k => vampireFields.includes(k));
              if (vampireUpdates.length > 0) {
                const vampireValues = vampireUpdates.map(f => updates[f]);
                const vampireSetClause = vampireUpdates.map(f => `${f} = ?`).join(', ');
                this.db.prepare(`UPDATE character_vampire_traits SET ${vampireSetClause} WHERE character_id = ?`)
                  .run(...vampireValues, character_id);
              }
              break;

            case 'werewolf':
              const werewolfUpdates = Object.keys(updates).filter(k => werewolfFields.includes(k));
              if (werewolfUpdates.length > 0) {
                const werewolfValues = werewolfUpdates.map(f => updates[f]);
                const werewolfSetClause = werewolfUpdates.map(f => `${f} = ?`).join(', ');
                this.db.prepare(`UPDATE character_werewolf_traits SET ${werewolfSetClause} WHERE character_id = ?`)
                  .run(...werewolfValues, character_id);
              }
              break;

            case 'mage':
              const mageUpdates = Object.keys(updates).filter(k => mageFields.includes(k));
              if (mageUpdates.length > 0) {
                const mageValues = mageUpdates.map(f => updates[f]);
                const mageSetClause = mageUpdates.map(f => `${f} = ?`).join(', ');
                this.db.prepare(`UPDATE character_mage_traits SET ${mageSetClause} WHERE character_id = ?`)
                  .run(...mageValues, character_id);
              }
              break;

            case 'changeling':
              const changelingUpdates = Object.keys(updates).filter(k => changelingFields.includes(k));
              if (changelingUpdates.length > 0) {
                const changelingValues = changelingUpdates.map(f => updates[f]);
                const changelingSetClause = changelingUpdates.map(f => `${f} = ?`).join(', ');
                this.db.prepare(`UPDATE character_changeling_traits SET ${changelingSetClause} WHERE character_id = ?`)
                  .run(...changelingValues, character_id);
              }
              break;
          }
        }

        // Return fresh character data to prevent state staleness
        return this.getCharacter(character_id);
      })();

      return result;
    } finally {
      // Always release the character lock
      this.releaseCharacterLock(character_id);
    }
  }

  applyHealthLevelDamage(targetType: string, targetId: number, damage: DamageObject) {
    // Robust, HealthTracker-based health/damage logic for target (character or npc)
    const table = targetType === 'character' ? 'characters' : 'npcs';
    let rec: any = this.db.prepare(`SELECT id, health_levels FROM ${table} WHERE id = ?`).get(targetId);
    if (!rec) return { success: false, message: `${targetType} not found` };

    const tracker = HealthTracker.from(rec.health_levels);
    const changed = tracker.applyDamage(damage);

    // Save back if changed
    if (changed) {
      this.db.prepare(`UPDATE ${table} SET health_levels = ? WHERE id = ?`).run(tracker.serialize(), targetId);
    }

    const boxes = tracker.getBoxArray();
    const penalty = tracker.getWoundPenalty();
    const isIncapacitated = tracker.isIncapacitated();
    const healthStatus = tracker.getHealthStatus();
    const statusText = `Health: ${boxes.join('|')} | Status: ${healthStatus} | Penalty: ${penalty}`;

    return {
      success: true,
      newHealth: boxes,
      woundPenalty: penalty,
      isIncapacitated,
      healthStatus,
      statusText
    };
  }

  // Add an entry to the experience_ledger for XP tracking
  addExperienceLedgerEntry(character_id: number, amount: number, reason: string) {
    this.db.prepare(
      `INSERT INTO experience_ledger (character_id, amount, reason) VALUES (?, ?, ?)`
    ).run(character_id, amount, reason);
  }

  // --- Antagonist & Character Management ---
  updateAntagonist(npc_id: number, updates: Record<string, any>): AntagonistRow | null {
    // Use transaction to ensure atomicity and prevent caching issues
    const result = this.db.transaction(() => {
      // This reuses the same logic as updateCharacter but targets the 'npcs' table
      // and its related modular trait tables. This will require a more complex,
      // game-line-aware update transaction, similar to createAntagonist.
      // For now, a simple core update:
      const scalarFields = Object.keys(updates).filter(k => !['abilities', 'disciplines'].includes(k));
      if (scalarFields.length > 0) {
        const setClause = scalarFields.map(f => `${f} = ?`).join(', ');
        this.db.prepare(`UPDATE npcs SET ${setClause} WHERE id = ?`).run(...scalarFields.map(k => updates[k]), npc_id);
      }
      // TODO: Add logic to update relational tables (abilities, disciplines, etc.)

      // Return fresh data to prevent cache staleness
      return this.getAntagonistById(npc_id);
    })();

    return result;
  }

  // Trait improvement with immediate state refresh
  improveTrait(character_id: number, trait_type: string, trait_name: string): { new_rating: number, trait_type: string, trait_name: string, xp_cost: number, updated_character: any } {
    // Acquire character lock to prevent concurrent modifications
    if (!this.acquireCharacterLock(character_id, 'improve_trait')) {
      throw new Error(`Character ${character_id} is currently being updated by another operation. Please try again.`);
    }

    try {
      const char = this.getCharacter(character_id);
      if (!char) throw new Error("Character not found.");

      let curr_rating = 0;
      // Get the current value depending on the trait_type
      switch (trait_type) {
        case 'attribute':
          curr_rating = char[trait_name];
          if (typeof curr_rating !== "number") throw new Error(`Attribute '${trait_name}' not found or invalid.`);
          break;
        case 'willpower':
          curr_rating = char['willpower_permanent'];
          if (typeof curr_rating !== "number") throw new Error("Willpower not found.");
          break;
        case 'power_stat':
          if (char.power_stat_name === trait_name) {
            curr_rating = char.power_stat_rating || 0;
          } else {
            throw new Error(`Power stat '${trait_name}' does not match character's power stat '${char.power_stat_name}'.`);
          }
          break;
        case 'ability':
          const ability = char.abilities?.find((a: any) => a.ability_name === trait_name);
          curr_rating = ability ? ability.rating : 0;
          break;
        case 'discipline':
          const discipline = char.disciplines?.find((d: any) => d.discipline_name === trait_name);
          curr_rating = discipline ? discipline.rating : 0;
          break;
        case 'sphere':
          const sphere = char.spheres?.find((s: any) => s.sphere_name === trait_name);
          curr_rating = sphere ? sphere.rating : 0;
          break;
        case 'art':
          const art = char.arts?.find((a: any) => a.art_name === trait_name);
          curr_rating = art ? art.rating : 0;
          break;
        case 'realm':
          const realm = char.realms?.find((r: any) => r.realm_name === trait_name);
          curr_rating = realm ? realm.rating : 0;
          break;
        default:
          throw new Error(`Unsupported trait_type: ${trait_type}`);
      }

      const new_rating = curr_rating + 1;
      let xp_cost = 0;

      switch (trait_type) {
        case 'attribute':
          xp_cost = new_rating * 4;
          break;
        case 'ability':
          xp_cost = new_rating * 2;
          break;
        case 'discipline':
          xp_cost = new_rating * 5;
          break;
        case 'sphere':
        case 'art':
        case 'realm':
          xp_cost = new_rating * 7;
          break;
        case 'willpower':
          xp_cost = 8;
          break;
        case 'power_stat':
          xp_cost = new_rating * 8;
          break;
        default:
          throw new Error(`Unsupported trait_type: ${trait_type}`);
      }

      // Validation: enough XP
      if ((char.experience || 0) < xp_cost) {
        throw new Error(`Not enough XP. Has ${char.experience}, needs ${xp_cost}.`);
      }

      // Perform the upgrade atomically
      const updated_character = this.db.transaction(() => {
        switch (trait_type) {
          case 'attribute': {
            const updateData: any = { experience: char.experience - xp_cost };
            updateData[trait_name] = new_rating;
            this.updateCharacter(character_id, updateData);
            break;
          }
          case 'willpower': {
            // Update willpower and experience directly to avoid recursive lock
            this.db.prepare('UPDATE characters SET willpower_permanent = ?, experience = ? WHERE id = ?')
              .run(new_rating, char.experience - xp_cost, character_id);
            break;
          }
          case 'power_stat': {
            // Update power stat and experience directly to avoid recursive lock
            this.db.prepare('UPDATE characters SET power_stat_rating = ?, experience = ? WHERE id = ?')
              .run(new_rating, char.experience - xp_cost, character_id);
            break;
          }
          case 'ability': {
            // Update or insert ability
            const existing = this.db.prepare('SELECT * FROM character_abilities WHERE character_id = ? AND ability_name = ?').get(character_id, trait_name);
            if (existing) {
              this.db.prepare('UPDATE character_abilities SET rating = ? WHERE character_id = ? AND ability_name = ?').run(new_rating, character_id, trait_name);
            } else {
              this.db.prepare('INSERT INTO character_abilities (character_id, ability_name, rating) VALUES (?, ?, ?)').run(character_id, trait_name, new_rating);
            }
            // Update experience directly to avoid recursive lock
            this.db.prepare('UPDATE characters SET experience = ? WHERE id = ?').run(char.experience - xp_cost, character_id);
            break;
          }
          case 'discipline': {
            // Update or insert discipline
            const existing = this.db.prepare('SELECT * FROM character_disciplines WHERE character_id = ? AND discipline_name = ?').get(character_id, trait_name);
            if (existing) {
              this.db.prepare('UPDATE character_disciplines SET rating = ? WHERE character_id = ? AND discipline_name = ?').run(new_rating, character_id, trait_name);
            } else {
              this.db.prepare('INSERT INTO character_disciplines (character_id, discipline_name, rating) VALUES (?, ?, ?)').run(character_id, trait_name, new_rating);
            }
            // Update experience directly to avoid recursive lock
            this.db.prepare('UPDATE characters SET experience = ? WHERE id = ?').run(char.experience - xp_cost, character_id);
            break;
          }
          case 'sphere': {
            // Update or insert sphere
            const existing = this.db.prepare('SELECT * FROM character_spheres WHERE character_id = ? AND sphere_name = ?').get(character_id, trait_name);
            if (existing) {
              this.db.prepare('UPDATE character_spheres SET rating = ? WHERE character_id = ? AND sphere_name = ?').run(new_rating, character_id, trait_name);
            } else {
              this.db.prepare('INSERT INTO character_spheres (character_id, sphere_name, rating) VALUES (?, ?, ?)').run(character_id, trait_name, new_rating);
            }
            // Update experience directly to avoid recursive lock
            this.db.prepare('UPDATE characters SET experience = ? WHERE id = ?').run(char.experience - xp_cost, character_id);
            break;
          }
          case 'art': {
            // Update or insert art
            const existing = this.db.prepare('SELECT * FROM character_arts WHERE character_id = ? AND art_name = ?').get(character_id, trait_name);
            if (existing) {
              this.db.prepare('UPDATE character_arts SET rating = ? WHERE character_id = ? AND art_name = ?').run(new_rating, character_id, trait_name);
            } else {
              this.db.prepare('INSERT INTO character_arts (character_id, art_name, rating) VALUES (?, ?, ?)').run(character_id, trait_name, new_rating);
            }
            // Update experience directly to avoid recursive lock
            this.db.prepare('UPDATE characters SET experience = ? WHERE id = ?').run(char.experience - xp_cost, character_id);
            break;
          }
          case 'realm': {
            // Update or insert realm
            const existing = this.db.prepare('SELECT * FROM character_realms WHERE character_id = ? AND realm_name = ?').get(character_id, trait_name);
            if (existing) {
              this.db.prepare('UPDATE character_realms SET rating = ? WHERE character_id = ? AND realm_name = ?').run(new_rating, character_id, trait_name);
            } else {
              this.db.prepare('INSERT INTO character_realms (character_id, realm_name, rating) VALUES (?, ?, ?)').run(character_id, trait_name, new_rating);
            }
            // Update experience directly to avoid recursive lock
            this.db.prepare('UPDATE characters SET experience = ? WHERE id = ?').run(char.experience - xp_cost, character_id);
            break;
          }
        }

        // Return fresh character data with all updates applied
        return this.getCharacter(character_id);
      })();

      return { new_rating, trait_type, trait_name, xp_cost, updated_character };
    } finally {
      // Always release the character lock
      this.releaseCharacterLock(character_id);
    }
  }

  // --- Resource Locking and Atomic Operations ---

  private getLockKey(character_id: number, resource_name: string): string {
    return `${character_id}:${resource_name}`;
  }

  private acquireResourceLock(character_id: number, resource_name: string, operation: string): boolean {
    const lockKey = this.getLockKey(character_id, resource_name);
    const now = Date.now();

    // Clean up expired locks
    this.cleanupExpiredLocks();

    // Check if resource is already locked
    const existingLock = this.resourceLocks.get(lockKey);
    if (existingLock) {
      // Check if lock has expired
      if (now - existingLock.timestamp > this.LOCK_TIMEOUT_MS) {
        this.resourceLocks.delete(lockKey);
      } else {
        return false; // Resource is locked
      }
    }

    // Acquire the lock
    this.resourceLocks.set(lockKey, { timestamp: now, operation });
    return true;
  }

  private releaseResourceLock(character_id: number, resource_name: string): void {
    const lockKey = this.getLockKey(character_id, resource_name);
    this.resourceLocks.delete(lockKey);
  }

  private cleanupExpiredLocks(): void {
    const now = Date.now();
    for (const [key, lock] of this.resourceLocks.entries()) {
      if (now - lock.timestamp > this.LOCK_TIMEOUT_MS) {
        this.resourceLocks.delete(key);
      }
    }
  }

  // Atomic resource operation with locking
  atomicResourceOperation(
    character_id: number,
    resource_name: string,
    operation: 'spend' | 'gain' | 'restore',
    amount: number,
    maxValue?: number
  ): { success: boolean; newValue?: number; error?: string } {

    // Acquire lock
    if (!this.acquireResourceLock(character_id, resource_name, operation)) {
      return {
        success: false,
        error: `Resource ${resource_name} is currently locked by another operation. Please try again.`
      };
    }

    try {
      // Resource mapping to database columns
      const resourceMap: Record<string, { current: string, max?: string }> = {
        willpower: { current: 'willpower_current', max: 'willpower_permanent' },
        blood: { current: 'blood_pool_current', max: 'blood_pool_max' },
        rage: { current: 'rage_current', max: 'rage_permanent' },
        gnosis: { current: 'gnosis_current', max: 'gnosis_permanent' },
        glamour: { current: 'glamour_current', max: 'glamour_permanent' },
        quintessence: { current: 'quintessence' },
        paradox: { current: 'paradox' },
        experience: { current: 'experience' }
      };

      const resourceConfig = resourceMap[resource_name];
      if (!resourceConfig) {
        return { success: false, error: `Unknown resource: ${resource_name}` };
      }

      // Perform atomic operation within transaction
      const result = this.db.transaction(() => {
        // Get current character state with all splat-specific data
        const char = this.getCharacter(character_id);
        if (!char) {
          throw new Error('Character not found');
        }

        const currentValue = char[resourceConfig.current] || 0;
        let newValue: number;
        let effectiveMaxValue = maxValue;

        // Calculate max value if not provided
        if (!effectiveMaxValue && resourceConfig.max) {
          effectiveMaxValue = char[resourceConfig.max] || 0;
        }

        // Calculate new value based on operation
        switch (operation) {
          case 'spend':
            if (currentValue < amount) {
              throw new Error(`Insufficient ${resource_name}. Current: ${currentValue}, trying to spend: ${amount}`);
            }
            newValue = currentValue - amount;
            if (newValue < 0) {
              throw new Error(`Operation would result in negative ${resource_name}`);
            }
            break;

          case 'gain':
          case 'restore':
            newValue = currentValue + amount;
            if (effectiveMaxValue && newValue > effectiveMaxValue) {
              newValue = effectiveMaxValue;
            }
            break;

          default:
            throw new Error(`Invalid operation: ${operation}`);
        }

        // Update the character with new value directly to avoid potential recursive locks
        const column = resourceConfig.current;

        // Check if this is a core field or splat-specific field
        const coreFields = ['willpower_current', 'willpower_permanent', 'experience'];

        if (coreFields.includes(column)) {
          // Update core character table
          this.db.prepare(`UPDATE characters SET \`${column}\` = ? WHERE id = ?`).run(newValue, character_id);
        } else {
          // Update appropriate splat-specific table
          // We already have char from above, so use it directly
          switch (char.game_line) {
            case 'vampire':
              if (['blood_pool_current', 'blood_pool_max', 'humanity'].includes(column)) {
                this.db.prepare(`UPDATE character_vampire_traits SET \`${column}\` = ? WHERE character_id = ?`).run(newValue, character_id);
              }
              break;
            case 'werewolf':
              if (['rage_current', 'rage_permanent', 'gnosis_current', 'gnosis_permanent'].includes(column)) {
                this.db.prepare(`UPDATE character_werewolf_traits SET \`${column}\` = ? WHERE character_id = ?`).run(newValue, character_id);
              }
              break;
            case 'mage':
              if (['quintessence', 'paradox'].includes(column)) {
                this.db.prepare(`UPDATE character_mage_traits SET \`${column}\` = ? WHERE character_id = ?`).run(newValue, character_id);
              }
              break;
            case 'changeling':
              if (['glamour_current', 'glamour_permanent'].includes(column)) {
                this.db.prepare(`UPDATE character_changeling_traits SET \`${column}\` = ? WHERE character_id = ?`).run(newValue, character_id);
              }
              break;
          }
        }

        return newValue;
      })();

      return { success: true, newValue: result };

    } catch (error: any) {
      return { success: false, error: error.message };
    } finally {
      // Always release the lock
      this.releaseResourceLock(character_id, resource_name);
    }
  }

  listCharacters(): any[] {
    return this.db.prepare('SELECT id, name, game_line, concept FROM characters ORDER BY name').all();
  }

  listAntagonists(): any[] {
    return this.db.prepare('SELECT id, name, template, game_line, concept FROM npcs ORDER BY name').all();
  }

  // --- Inventory Management ---
  addItem(character_id: number, item: { name: string; type?: string; quantity?: number; description?: string; properties?: any; }) {
    const stmt = this.db.prepare(`
      INSERT INTO inventory (character_id, item_name, item_type, quantity, description, properties)
      VALUES (?, ?, ?, ?, ?, ?)
    `);
    const result = stmt.run(
      character_id,
      item.name,
      item.type || 'misc',
      item.quantity || 1,
      item.description || null,
      item.properties ? JSON.stringify(item.properties) : null
    );
    return { id: result.lastInsertRowid, ...item };
  }

  getInventory(character_id: number) {
    const stmt = this.db.prepare('SELECT * FROM inventory WHERE character_id = ?');
    return stmt.all(character_id).map((item: any) => ({
      ...item,
      properties: item.properties ? JSON.parse(item.properties) : null,
    }));
  }

  updateItem(item_id: number, updates: { quantity?: number; description?: string; properties?: any }) {
    const fields = Object.keys(updates);
    const values = fields.map(key => {
      const value = (updates as any)[key];
      return typeof value === 'object' ? JSON.stringify(value) : value;
    });

    if (fields.length === 0) return;
    const setClause = fields.map(f => `${f} = ?`).join(', ');
    this.db.prepare(`UPDATE inventory SET ${setClause} WHERE id = ?`).run(...values, item_id);
  }

  removeItem(item_id: number) {
    const res = this.db.prepare('DELETE FROM inventory WHERE id = ?').run(item_id);
    return res.changes > 0;
  }

}

// Test script for high-priority fixes
// Tests state propagation, cache invalidation, and operation serialization

const { GameDatabase } = require('./game-state-server/src/db.js');

async function testHighPriorityFixes() {
  console.log('🧪 Testing High-Priority Fixes...\n');
  
  const db = new GameDatabase();
  
  // Test 1: State Propagation Delay Fix
  console.log('Test 1: State Propagation Delay Fix');
  console.log('====================================');
  
  try {
    // Create a test character
    const testChar = db.createCharacter({
      name: 'State Test Character',
      concept: 'Test Subject',
      game_line: 'vampire',
      willpower_current: 3,
      willpower_permanent: 5,
      blood_pool_current: 8,
      blood_pool_max: 12,
      experience: 20
    });
    
    console.log(`✅ Created test character: ${testChar.name} (ID: ${testChar.id})`);
    console.log(`   Initial XP: ${testChar.experience}`);
    
    // Test immediate state refresh after update
    console.log('\n🔄 Testing immediate state refresh...');
    
    const updateResult = db.updateCharacter(testChar.id, { 
      experience: 25,
      willpower_current: 4 
    });
    
    if (updateResult) {
      console.log(`✅ Update returned fresh data immediately:`);
      console.log(`   XP: ${updateResult.experience} (expected: 25)`);
      console.log(`   Willpower: ${updateResult.willpower_current} (expected: 4)`);
      
      // Verify the data is consistent
      const freshLookup = db.getCharacter(testChar.id);
      const consistent = freshLookup.experience === updateResult.experience && 
                        freshLookup.willpower_current === updateResult.willpower_current;
      console.log(`✅ State consistency check: ${consistent ? 'PASSED' : 'FAILED'}`);
    } else {
      console.log('❌ Update did not return fresh data');
    }
    
    // Test 2: Cache Invalidation Strategy
    console.log('\n\nTest 2: Cache Invalidation Strategy');
    console.log('===================================');
    
    console.log('🔄 Testing rapid sequential lookups...');
    
    // Perform rapid updates and lookups
    const rapidTests = [];
    for (let i = 0; i < 5; i++) {
      const startTime = Date.now();
      
      // Update
      const updated = db.updateCharacter(testChar.id, { experience: 25 + i });
      
      // Immediate lookup
      const lookup = db.getCharacter(testChar.id);
      
      const endTime = Date.now();
      const consistent = updated && lookup && updated.experience === lookup.experience;
      
      rapidTests.push({
        iteration: i + 1,
        duration: endTime - startTime,
        consistent,
        expectedXP: 25 + i,
        actualXP: lookup ? lookup.experience : 'null'
      });
    }
    
    console.log('\n📊 Rapid Update/Lookup Results:');
    rapidTests.forEach(test => {
      const status = test.consistent ? '✅' : '❌';
      console.log(`  ${status} Iteration ${test.iteration}: ${test.duration}ms, XP: ${test.actualXP} (expected: ${test.expectedXP})`);
    });
    
    const allConsistent = rapidTests.every(test => test.consistent);
    console.log(`\n🎯 Cache consistency: ${allConsistent ? 'PASSED' : 'FAILED'}`);
    
    // Test 3: Operation Serialization
    console.log('\n\nTest 3: Operation Serialization');
    console.log('===============================');
    
    console.log('🔄 Testing concurrent character updates...');
    
    // Test concurrent updates to the same character
    const concurrentPromises = [];
    
    for (let i = 0; i < 5; i++) {
      concurrentPromises.push(
        new Promise((resolve) => {
          setTimeout(() => {
            try {
              const result = db.updateCharacter(testChar.id, { 
                experience: 30 + i,
                willpower_current: 2 + i 
              });
              resolve({ 
                operation: `update-${i}`, 
                success: !!result, 
                experience: result ? result.experience : null,
                error: null 
              });
            } catch (error) {
              resolve({ 
                operation: `update-${i}`, 
                success: false, 
                experience: null,
                error: error.message 
              });
            }
          }, Math.random() * 50);
        })
      );
    }
    
    const concurrentResults = await Promise.all(concurrentPromises);
    
    console.log('\n📊 Concurrent Update Results:');
    concurrentResults.forEach(result => {
      const status = result.success ? '✅' : '❌';
      const message = result.success ? 
        `XP: ${result.experience}` : 
        `Error: ${result.error}`;
      console.log(`  ${status} ${result.operation}: ${message}`);
    });
    
    // Check final state consistency
    const finalState = db.getCharacter(testChar.id);
    console.log(`\n📈 Final character state:`);
    console.log(`  Experience: ${finalState.experience}`);
    console.log(`  Willpower: ${finalState.willpower_current}`);
    
    // Test 4: Level-up State Propagation
    console.log('\n\nTest 4: Level-up State Propagation');
    console.log('==================================');
    
    // Test trait improvement with immediate state refresh
    console.log('🔄 Testing trait improvement with immediate state refresh...');
    
    try {
      // Ensure character has enough XP
      db.updateCharacter(testChar.id, { experience: 50 });
      
      // Improve a trait
      const improvementResult = db.improveTrait(testChar.id, 'attribute', 'strength');
      
      console.log(`✅ Trait improvement completed:`);
      console.log(`   Trait: ${improvementResult.trait_name}`);
      console.log(`   New rating: ${improvementResult.new_rating}`);
      console.log(`   XP cost: ${improvementResult.xp_cost}`);
      console.log(`   Updated character returned: ${!!improvementResult.updated_character}`);
      
      if (improvementResult.updated_character) {
        console.log(`   Character XP after improvement: ${improvementResult.updated_character.experience}`);
        console.log(`   Character strength: ${improvementResult.updated_character.strength}`);
        
        // Verify immediate consistency
        const freshLookup = db.getCharacter(testChar.id);
        const xpConsistent = freshLookup.experience === improvementResult.updated_character.experience;
        const traitConsistent = freshLookup.strength === improvementResult.updated_character.strength;
        
        console.log(`✅ XP consistency: ${xpConsistent ? 'PASSED' : 'FAILED'}`);
        console.log(`✅ Trait consistency: ${traitConsistent ? 'PASSED' : 'FAILED'}`);
      }
      
    } catch (error) {
      console.log(`❌ Trait improvement failed: ${error.message}`);
    }
    
    // Test 5: Character Lock Serialization
    console.log('\n\nTest 5: Character Lock Serialization');
    console.log('====================================');
    
    console.log('🔄 Testing character lock prevents concurrent modifications...');
    
    // Test that character locks prevent race conditions
    const lockTestPromises = [];
    
    for (let i = 0; i < 3; i++) {
      lockTestPromises.push(
        new Promise((resolve) => {
          setTimeout(() => {
            try {
              const result = db.updateCharacter(testChar.id, { 
                concept: `Updated Concept ${i}` 
              });
              resolve({ 
                operation: `lock-test-${i}`, 
                success: !!result, 
                concept: result ? result.concept : null 
              });
            } catch (error) {
              resolve({ 
                operation: `lock-test-${i}`, 
                success: false, 
                error: error.message 
              });
            }
          }, i * 10); // Stagger slightly
        })
      );
    }
    
    const lockResults = await Promise.all(lockTestPromises);
    
    console.log('\n📊 Character Lock Test Results:');
    lockResults.forEach(result => {
      const status = result.success ? '✅' : '❌';
      const message = result.success ? 
        `Concept: "${result.concept}"` : 
        `Error: ${result.error}`;
      console.log(`  ${status} ${result.operation}: ${message}`);
    });
    
    const successCount = lockResults.filter(r => r.success).length;
    const lockCount = lockResults.filter(r => !r.success && r.error.includes('currently being updated')).length;
    
    console.log(`\n📈 Lock effectiveness:`);
    console.log(`  Successful updates: ${successCount}`);
    console.log(`  Blocked by locks: ${lockCount}`);
    console.log(`  Lock system working: ${lockCount > 0 ? 'YES' : 'NO'}`);
    
    console.log('\n🎉 All high-priority fixes tested successfully!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error(error.stack);
  }
}

// Run the tests
testHighPriorityFixes().catch(console.error);

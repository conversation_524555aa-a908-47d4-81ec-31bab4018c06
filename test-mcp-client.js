// Test MCP client to properly test the server
import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';
import { spawn } from 'child_process';

console.log('🧪 Testing MCP Client Connection...\n');

async function testMCPConnection() {
  try {
    // Start the server process
    const serverProcess = spawn('node', ['dist/index.js'], {
      cwd: './game-state-server',
      stdio: ['pipe', 'pipe', 'pipe']
    });

    // Create MCP client
    const transport = new StdioClientTransport({
      command: 'node',
      args: ['dist/index.js'],
      cwd: './game-state-server'
    });

    const client = new Client({
      name: 'test-client',
      version: '1.0.0'
    }, {
      capabilities: {}
    });

    console.log('📡 Connecting to server...');
    await client.connect(transport);
    console.log('✅ Connected to server');

    console.log('📋 Listing tools...');
    const toolsResult = await client.listTools();
    console.log('✅ Tools listed successfully:');
    console.log(`Found ${toolsResult.tools.length} tools:`);
    
    toolsResult.tools.slice(0, 5).forEach(tool => {
      console.log(`  - ${tool.name}: ${tool.description}`);
    });
    
    if (toolsResult.tools.length > 5) {
      console.log(`  ... and ${toolsResult.tools.length - 5} more tools`);
    }

    console.log('\n🎯 Testing a tool call...');
    try {
      const result = await client.callTool({
        name: 'list_characters',
        arguments: {}
      });
      console.log('✅ Tool call successful:', result.content[0].text);
    } catch (error) {
      console.log('⚠️ Tool call failed:', error.message);
    }

    await client.close();
    console.log('✅ Client disconnected');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error(error.stack);
  }
}

testMCPConnection();

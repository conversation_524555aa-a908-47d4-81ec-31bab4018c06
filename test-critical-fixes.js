// Test script for critical resource management fixes
// This script tests the atomic operations and locking mechanisms

const { GameDatabase } = require('./game-state-server/src/db.js');

async function testCriticalFixes() {
  console.log('🧪 Testing Critical Resource Management Fixes...\n');
  
  const db = new GameDatabase();
  
  // Test 1: Resource Locking Mechanism
  console.log('Test 1: Resource Locking Mechanism');
  console.log('=====================================');
  
  try {
    // Create a test character
    const testChar = db.createCharacter({
      name: 'Test Character',
      concept: 'Test Subject',
      game_line: 'vampire',
      willpower_current: 5,
      willpower_permanent: 5,
      blood_pool_current: 10,
      blood_pool_max: 15
    });
    
    console.log(`✅ Created test character: ${testChar.name} (ID: ${testChar.id})`);
    
    // Test concurrent resource operations
    console.log('\n🔄 Testing concurrent resource operations...');
    
    const promises = [];
    
    // Simulate multiple simultaneous spend operations
    for (let i = 0; i < 5; i++) {
      promises.push(
        new Promise((resolve) => {
          setTimeout(() => {
            const result = db.atomicResourceOperation(testChar.id, 'willpower', 'spend', 1);
            resolve({ operation: `spend-${i}`, result });
          }, Math.random() * 100);
        })
      );
    }
    
    // Simulate multiple simultaneous gain operations
    for (let i = 0; i < 3; i++) {
      promises.push(
        new Promise((resolve) => {
          setTimeout(() => {
            const result = db.atomicResourceOperation(testChar.id, 'blood', 'gain', 1, 15);
            resolve({ operation: `gain-${i}`, result });
          }, Math.random() * 100);
        })
      );
    }
    
    const results = await Promise.all(promises);
    
    console.log('\n📊 Concurrent Operation Results:');
    results.forEach(({ operation, result }) => {
      const status = result.success ? '✅' : '❌';
      const message = result.success ? `New value: ${result.newValue}` : result.error;
      console.log(`  ${status} ${operation}: ${message}`);
    });
    
    // Verify final state
    const finalChar = db.getCharacterById(testChar.id);
    console.log(`\n📈 Final character state:`);
    console.log(`  Willpower: ${finalChar.willpower_current}/${finalChar.willpower_permanent}`);
    console.log(`  Blood: ${finalChar.blood_pool_current}/${finalChar.blood_pool_max}`);
    
    // Test 2: Race Condition Prevention
    console.log('\n\nTest 2: Race Condition Prevention');
    console.log('==================================');
    
    // Test rapid sequential operations
    console.log('🏃 Testing rapid sequential operations...');
    
    const rapidResults = [];
    for (let i = 0; i < 10; i++) {
      const result = db.atomicResourceOperation(testChar.id, 'blood', 'spend', 1);
      rapidResults.push(result);
    }
    
    const successCount = rapidResults.filter(r => r.success).length;
    const failCount = rapidResults.filter(r => !r.success).length;
    
    console.log(`✅ Successful operations: ${successCount}`);
    console.log(`❌ Failed operations: ${failCount}`);
    
    // Test 3: Negative Resource Prevention
    console.log('\n\nTest 3: Negative Resource Prevention');
    console.log('====================================');
    
    // Try to spend more than available
    const overSpendResult = db.atomicResourceOperation(testChar.id, 'willpower', 'spend', 100);
    console.log(`🚫 Over-spend attempt: ${overSpendResult.success ? 'FAILED - Should have been blocked!' : 'SUCCESS - Properly blocked'}`);
    if (!overSpendResult.success) {
      console.log(`   Error message: ${overSpendResult.error}`);
    }
    
    // Test 4: Lock Timeout
    console.log('\n\nTest 4: Lock Timeout Mechanism');
    console.log('===============================');
    
    // Manually test lock cleanup (this would normally happen automatically)
    db.cleanupExpiredLocks();
    console.log('✅ Lock cleanup completed successfully');
    
    // Test 5: Antagonist Update Caching Fix
    console.log('\n\nTest 5: Antagonist Update Caching Fix');
    console.log('=====================================');
    
    // Create a test antagonist
    const testAntagonist = db.createAntagonist('vampire_fledgling', 'Test Vampire');
    console.log(`✅ Created test antagonist: ${testAntagonist.name} (ID: ${testAntagonist.id})`);
    
    // Update and immediately read back
    const updateResult = db.updateAntagonist(testAntagonist.id, { concept: 'Updated Concept' });
    console.log(`✅ Updated antagonist concept: ${updateResult ? updateResult.concept : 'Update failed'}`);
    
    // Verify the update persisted
    const freshAntagonist = db.getAntagonistById(testAntagonist.id);
    const cacheTest = freshAntagonist.concept === 'Updated Concept' ? 'PASSED' : 'FAILED';
    console.log(`🔄 Cache consistency test: ${cacheTest}`);
    
    console.log('\n🎉 All critical fixes tested successfully!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error(error.stack);
  }
}

// Run the tests
testCriticalFixes().catch(console.error);
